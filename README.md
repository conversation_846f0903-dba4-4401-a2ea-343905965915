# IAA GameLog - BaseNet API 游戏服务

一个完全基于BaseNet API规范的高性能游戏服务，使用Go语言开发，支持SQLite和MongoDB两种数据库。

**核心功能：**
- 🎮 游戏配置管理（启动配置、分享配置、广告配置）
- 👤 多平台用户登录（微信、百度、QQ）
- 💾 安全的游戏数据存储（支持MD5签名验证）
- 📊 完整的统计上报（登录、游戏行为、广告、分享、提示统计）
- 🛠️ 实用工具接口（服务器时间、IP检查、登录码验证）
- 💬 微信生态支持（数据解密、分享卡片、分享信息）
- 📱 头条平台支持（内容审核、圈子礼品、奖励系统）
- 🔍 其他扩展功能（素材管理、广告信息、白名单检查）

## 功能特性

- 🚀 **高性能**: SQLite WAL模式优化，支持高并发读写
- 🗄️ **多数据库支持**: 支持SQLite和MongoDB，可灵活切换
- 📦 **轻量级**: 单个exe文件，无需额外依赖
- 🔧 **简单易用**: RESTful API设计，接口简洁明了
- 💾 **数据持久化**: 可靠的数据存储，支持事务处理
- 🌐 **跨域支持**: 内置CORS支持，便于前端集成
- ⚙️ **配置灵活**: YAML配置文件，支持多环境部署

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置数据库

项目使用YAML配置文件，默认配置文件为`config.yaml`：

```yaml
# 数据库配置
database:
  # 数据库类型: sqlite 或 mongodb
  type: sqlite
  
  # SQLite配置
  sqlite:
    path: ./game_records.db
  
  # MongoDB配置
  mongodb:
    uri: mongodb://localhost:27017
    database: gamelog
    collection: game_records

# 服务器配置
server:
  port: 8080
  host: localhost
  
  # CORS配置
  cors:
    enabled: true
    allowed_origins:
      - "*"
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed_headers:
      - "*"

# 日志配置
logging:
  level: info
  format: text
  output: stdout

# 安全配置
security:
  app_secret: "your_app_secret_key_here"

# BaseNet API配置
basenet:
  platforms:
    wechat:
      app_id: "your_wechat_app_id"
      app_secret: "your_wechat_app_secret"
  game_config:
    max_level: 100
    daily_reward: true
  ads_config:
    banner:
      enabled: true
```

### 3. 启动服务

```bash
# 编译
go build -o gamelog.exe .

# 运行
.\gamelog.exe
```

服务将在 `http://localhost:8080` 启动

## API 接口

### BaseNet API（游戏客户端专用）

**总计25个API接口，完全覆盖BaseNet API规范**

#### 游戏配置相关
- `GET /common/config/info` - 获取游戏启动配置
- `GET /common/game/share_list` - 获取分享配置
- `GET /common/game/ads` - 获取广告配置

#### 用户登录相关
- `POST /common/session/sign_in` - 微信登录
- `POST /common/baidu/sign_in` - 百度登录
- `POST /common/qqminiapp/sign_in` - QQ登录

#### 数据存储相关
- `POST /common/game-data/s-save` - 保存游戏数据（需要签名验证）
- `GET /common/game-data/get` - 获取游戏数据
- `GET /common/game-data/multi-get` - 批量获取游戏数据

#### 统计上报相关
- `POST /statistics/login_log` - 登录日志统计
- `POST /statistics/game` - 游戏行为统计
- `POST /statistics/ad/show` - 广告展示统计
- `POST /statistics/ad/hit` - 广告点击统计
- `POST /statistics/share/show` - 分享展示统计
- `POST /statistics/hint` - 提示统计

#### 工具类接口
- `GET /common/common/time` - 获取服务器时间
- `GET /common/is/is` - IP检查
- `GET /common/login-code/check` - 登录码验证

#### 微信相关接口
- `POST /common/wechat/decode_data` - 解密微信数据
- `POST /common/share/hit` - 分享卡片
- `GET /common/share/info` - 分享信息

#### 头条相关接口
- `POST /common/toutiao/antidirt` - 内容审核
- `POST /common/toutiao/gift-receive-reward` - 礼品奖励
- `GET /common/toutiao/get-unconsumed-circle-gifts` - 获取未消费圈子礼品
- `POST /common/toutiao/consume-circle-gift` - 消费圈子礼品

#### 其他接口
- `GET /common/ads/material-ss` - 素材接口
- `GET /api/sdk/yw/adv/getAdvInfo` - 获取广告信息
- `GET /check/Uuidwhitelist/uuidInWhitelist` - UUID白名单检查

#### API功能特性

**🔐 安全性**
- MD5签名验证机制，确保数据传输安全
- 登录码MD5加密处理
- 参数验证和错误处理

**🌐 平台支持**
- 微信小游戏生态完整支持
- 头条/抖音平台特色功能
- 多平台登录统一接口

**📊 数据统计**
- 完整的用户行为统计
- 广告展示和点击追踪
- 分享行为数据收集
- 提示和引导效果统计

**🎮 游戏功能**
- 圈子礼品系统
- 内容审核机制
- 素材管理接口
- 白名单权限控制

#### 签名验证机制

部分API（如`/common/game-data/s-save`）需要MD5签名验证：

**签名参数：**
- `timestamp`: 客户端时间戳（秒）
- `nonce`: 随机字符串
- `sign`: MD5签名

**签名算法：**
1. 将所有参数（除sign外）按字母排序
2. 拼接为 `key1=value1&key2=value2` 格式
3. 在末尾添加AppSecret
4. 计算MD5值作为签名

**示例：**
```javascript
// 参数
const params = {
  app_name: "test_app",
  uuid: "user123",
  d_key: "save_data",
  d_data: "{}",
  timestamp: **********,
  nonce: "abc123"
};

// 排序并拼接
const paramStr = "app_name=test_app&d_data={}&d_key=save_data&nonce=abc123&timestamp=**********&uuid=user123";

// 添加密钥并计算MD5
const sign = md5(paramStr + "your_app_secret_key_here");
```

### 系统接口

#### 健康检查

**GET** `/health`

**响应：**
```json
{
  "status": "ok"
}
```

## API 使用示例

### 1. 获取游戏配置

```bash
curl "http://localhost:8080/common/config/info?app_name=my_game&version=1.0.0"
```

### 2. 用户登录

```bash
curl -X POST "http://localhost:8080/common/session/sign_in" \
  -H "Content-Type: application/json" \
  -d '{"code": "wx_login_code", "app_name": "my_game"}'
```

### 3. 保存游戏数据（需要签名）

```bash
curl -X POST "http://localhost:8080/common/game-data/s-save" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "my_game",
    "version": "1.0.0",
    "uuid": "user123",
    "d_key": "player_data",
    "d_data": "{\"level\":5,\"score\":1000}",
    "timestamp": **********,
    "nonce": "abc123",
    "sign": "calculated_md5_signature"
  }'
```

### 4. 获取游戏数据

```bash
curl "http://localhost:8080/common/game-data/get?app_name=my_game&version=1.0.0&uuid=user123&d_key=player_data"
```

### 5. 微信数据解密

```bash
curl -X POST "http://localhost:8080/common/wechat/decode_data" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "my_game",
    "encrypted_data": "encrypted_user_data",
    "iv": "initialization_vector",
    "session_key": "wechat_session_key"
  }'
```

### 6. 获取分享信息

```bash
curl "http://localhost:8080/common/share/info?app_name=my_game&version=1.0.0&uuid=user123"
```

### 7. UUID白名单检查

```bash
curl "http://localhost:8080/check/Uuidwhitelist/uuidInWhitelist?uuid=test_user_001&app_name=my_game"
```

### 8. 头条圈子礼品

```bash
# 获取未消费礼品
curl "http://localhost:8080/common/toutiao/get-unconsumed-circle-gifts?app_name=my_game&version=1.0.0&uuid=user123"

# 消费礼品
curl -X POST "http://localhost:8080/common/toutiao/consume-circle-gift" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "my_game",
    "version": "1.0.0",
    "uuid": "user123",
    "gift_id": "gift_001"
  }'
```

## 模块化测试

项目提供了完整的模块化测试套件，可以独立测试各个功能模块：

### 🧪 单独测试模块

```bash
# 数据存储模块测试（包含签名验证）
go run test/test-save.go

# 游戏配置模块测试
go run test/test-config.go

# 用户登录模块测试
go run test/test-login.go

# 统计上报模块测试
go run test/test-stats.go

# 工具类模块测试
go run test/test-utils.go
```

### 🚀 全面测试

```bash
# 运行所有测试模块
go run test/test-all.go

# 查看可用的测试模块
go run test/test-all.go --list

# 运行指定模块测试
go run test/test-all.go --module 数据存储

# 查看帮助信息
go run test/test-all.go --help
```

### 📊 测试特性

- **模块化设计**：每个功能模块独立测试，便于调试
- **详细输出**：显示请求和响应详情，便于问题定位
- **自动验证**：自动检查API响应格式和数据正确性
- **签名测试**：完整测试MD5签名验证机制
- **错误处理**：测试各种边界情况和错误场景
- **服务检查**：自动检测服务器状态，确保测试环境正常



## 数据库支持

### SQLite（默认）

**优势：**
- 无需额外安装，开箱即用
- 高性能，适合中小型应用
- WAL模式支持并发读写
- 数据文件便于备份和迁移

**优化配置：**
- WAL模式：支持并发读写
- 连接池：优化并发性能
- 内存映射：提升访问速度
- 智能重试：处理并发冲突

### MongoDB

**优势：**
- 原生支持JSON文档
- 水平扩展能力强
- 适合复杂数据结构
- 高可用性支持

**使用MongoDB：**

1. 启动MongoDB服务：
```bash
# 使用Docker
docker run -d --name mongodb -p 27017:27017 mongo:latest
```

2. 修改配置文件：
```yaml
database:
  type: mongodb
  mongodb:
    uri: mongodb://localhost:27017
    database: gamelog
    collection: game_records
```

## 性能优化

### SQLite优化

- **WAL模式**: 支持多读者并发访问
- **连接池**: 10个最大连接，5个空闲连接
- **内存映射**: 256MB mmap提升访问速度
- **缓存优化**: 4000页缓存大小
- **重试机制**: 智能处理并发冲突

### 应用层优化

- **预编译SQL**: 提升查询性能
- **事务处理**: 保证数据一致性
- **分页查询**: 支持大数据量查询
- **CORS优化**: 减少预检请求

## 使用示例

### 使用curl测试

```bash
# 保存游戏存档（RESTful API）
curl -X PUT http://localhost:8080/api/my_game/saves/player001 \
  -H "Content-Type: application/json" \
  -d '{
    "level": 8,
    "score": 2500,
    "items": ["magic_sword", "health_potion"],
    "progress": {
      "chapter": 3,
      "boss_defeated": true
    }
  }'

# 获取游戏存档
curl http://localhost:8080/api/my_game/saves/player001

# 删除游戏存档
curl -X DELETE http://localhost:8080/api/my_game/saves/player001

# 健康检查
curl http://localhost:8080/health
```

### JavaScript示例

```javascript
// 保存游戏存档
async function saveGame(gameName, userId, gameData) {
  const response = await fetch(`/api/${gameName}/saves/${userId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(gameData)
  });
  return response.json();
}

// 加载游戏存档
async function loadGame(gameName, userId) {
  const response = await fetch(`/api/${gameName}/saves/${userId}`);
  if (response.ok) {
    return response.json();
  }
  return null;
}

// 使用示例
const gameData = {
  level: 5,
  score: 1000,
  inventory: ['sword', 'potion'],
  settings: {
    difficulty: 'normal',
    sound: true
  }
};

await saveGame('my_rpg', 'player123', gameData);
const savedData = await loadGame('my_rpg', 'player123');
```

## 部署说明

### 本地部署

1. 编译生成exe文件
2. 准备配置文件`config.yaml`
3. 直接运行exe文件
4. 数据库文件会自动创建

### Docker部署

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o gamelog .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/gamelog .
COPY --from=builder /app/config.yaml .
EXPOSE 8080
CMD ["./gamelog"]
```

```bash
# 构建镜像
docker build -t gamelog .

# 运行容器
docker run -d -p 8080:8080 --name gamelog-server gamelog
```

## 技术架构

### 核心组件

- **Web框架**: Gin - 高性能HTTP框架
- **数据库**: SQLite/MongoDB - 灵活的存储选择
- **配置管理**: Viper - YAML配置解析
- **日志系统**: 结构化日志输出

### 设计原则

- **统一接口**: 数据库抽象层，支持多种数据库
- **RESTful设计**: 符合REST规范的API设计
- **向后兼容**: 保持旧API的兼容性
- **性能优先**: 针对高并发场景优化
- **简单部署**: 最小化部署复杂度

## 注意事项

- 确保配置的端口未被占用
- SQLite数据库文件会自动创建在指定路径
- MongoDB需要单独安装和启动
- 服务重启不会丢失数据
- 建议在生产环境中配置适当的CORS策略
- 定期备份数据库文件（SQLite）或配置MongoDB副本集

## 技术栈

- **语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: SQLite 3.x / MongoDB 4.x+
- **配置**: Viper (YAML)
- **数据库驱动**: go-sqlite3 / mongo-driver